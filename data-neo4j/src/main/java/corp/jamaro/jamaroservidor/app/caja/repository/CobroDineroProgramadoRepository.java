package corp.jamaro.jamaroservidor.app.caja.repository;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface CobroDineroProgramadoRepository extends ReactiveNeo4jRepository<CobroDineroProgramado, UUID> {

    /**
     * Obtiene CobroDineroProgramado pendientes (estaCobrado=false) relacionados con Sales de tipo CONTADO
     * que no están pagados/entregados (estaPagadoEntregado=false).
     * Ordenados de más antiguo a más reciente por fechaLimite.
     * Retorna proyección que incluye tanto CobroDineroProgramado como Sale.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE s.tipoVenta = 'CONTADO'
           AND s.estaPagadoEntregado = false
           AND cdp.estaCobrado = false
           OPTIONAL MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
           RETURN {
               cobro: cdp,
               sale: s,
               cliente: c
           } as result
           ORDER BY cdp.fechaLimite ASC
           """)
    Flux<java.util.Map<String, Object>> findCobrosContadoPendientesWithSale();

    /**
     * Obtiene CobroDineroProgramado pendientes (estaCobrado=false) relacionados con Sales de tipo CREDITO
     * que no están pagados/entregados (estaPagadoEntregado=false).
     * Ordenados de más antiguo a más reciente por fechaLimite.
     * Con paginación para lazy loading.
     * Retorna proyección que incluye tanto CobroDineroProgramado como Sale.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE s.tipoVenta = 'CREDITO'
           AND s.estaPagadoEntregado = false
           AND cdp.estaCobrado = false
           OPTIONAL MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
           RETURN {
               cobro: cdp,
               sale: s,
               cliente: c
           } as result
           ORDER BY cdp.fechaLimite ASC
           SKIP $skip LIMIT $limit
           """)
    Flux<java.util.Map<String, Object>> findCobrosCreditoPendientesWithSale(@Param("skip") int skip, @Param("limit") int limit);

    /**
     * Cuenta el total de CobroDineroProgramado pendientes para ventas de CREDITO.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE s.tipoVenta = 'CREDITO'
           AND s.estaPagadoEntregado = false
           AND cdp.estaCobrado = false
           RETURN count(cdp)
           """)
    Mono<Long> countCobrosCreditoPendientes();

    /**
     * Obtiene CobroDineroProgramado pendientes (estaCobrado=false) relacionados con Sales de tipo PEDIDO
     * que no están pagados/entregados (estaPagadoEntregado=false).
     * Ordenados de más antiguo a más reciente por fechaLimite.
     * Retorna proyección que incluye tanto CobroDineroProgramado como Sale.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE s.tipoVenta = 'PEDIDO'
           AND s.estaPagadoEntregado = false
           AND cdp.estaCobrado = false
           OPTIONAL MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
           RETURN {
               cobro: cdp,
               sale: s,
               cliente: c
           } as result
           ORDER BY cdp.fechaLimite ASC
           """)
    Flux<java.util.Map<String, Object>> findCobrosPedidoPendientesWithSale();

    /**
     * Obtiene un CobroDineroProgramado con su Sale relacionado para operaciones de cobro.
     */
    @Query("""
           MATCH (cdp:CobroDineroProgramado) WHERE cdp.id = $cobroId
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp)
           OPTIONAL MATCH (s)-[:CON_CLIENTE]->(c:Cliente)
           RETURN cdp, s, c
           """)
    Mono<CobroDineroProgramado> findCobroWithSale(@Param("cobroId") UUID cobroId);

    /**
     * Crea relación entre Caja y Dinero (entrada).
     */
    @Query("""
           MATCH (c:Caja) WHERE c.id = $cajaId
           MATCH (d:Dinero) WHERE d.id = $dineroId
           CREATE (c)-[:CON_ENTRADA_CAJA]->(d)
           RETURN d.id
           """)
    Mono<UUID> createCajaDineroRelation(@Param("cajaId") UUID cajaId, @Param("dineroId") UUID dineroId);

    /**
     * Crea relación entre CobroDineroProgramado y Dinero.
     */
    @Query("""
           MATCH (cdp:CobroDineroProgramado) WHERE cdp.id = $cobroId
           MATCH (d:Dinero) WHERE d.id = $dineroId
           CREATE (cdp)-[:CON_DINERO_COBRADO]->(d)
           RETURN d.id
           """)
    Mono<UUID> createCobroDineroRelation(@Param("cobroId") UUID cobroId, @Param("dineroId") UUID dineroId);

    /**
     * Actualiza los montos del CobroDineroProgramado después de un cobro.
     */
    @Query("""
           MATCH (cdp:CobroDineroProgramado) WHERE cdp.id = $cobroId
           SET cdp.montoRestante = $nuevoMontoRestante,
               cdp.estaCobrado = CASE WHEN $nuevoMontoRestante <= 0 THEN true ELSE false END,
               cdp.terminadoDeCobrarEl = CASE WHEN $nuevoMontoRestante <= 0 THEN $now ELSE cdp.terminadoDeCobrarEl END
           RETURN cdp
           """)
    Mono<CobroDineroProgramado> updateCobroMontos(@Param("cobroId") UUID cobroId,
                                                  @Param("nuevoMontoRestante") Double nuevoMontoRestante,
                                                  @Param("now") java.time.Instant now);
}
