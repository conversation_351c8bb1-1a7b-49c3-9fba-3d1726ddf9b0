package corp.jamaro.jamaroservidor.app.caja.repository;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface CobroDineroProgramadoRepository extends ReactiveNeo4jRepository<CobroDineroProgramado, UUID> {

    /**
     * Encuentra CobroDineroProgramado con Sale de tipo CONTADO pendientes de cobro.
     * Retorna tuplas (CobroDineroProgramado, Sale) ordenadas por creadoEl más antiguo a más reciente.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE cdp.estaCobrado = false
           AND s.tipoVenta = 'CONTADO'
           AND s.estaPagadoEntregado = false
           RETURN cdp, s
           ORDER BY cdp.creadoEl ASC
           """)
    Flux<Object[]> findCobroContadoPendientes();

    /**
     * Encuentra CobroDineroProgramado con Sale de tipo CREDITO pendientes de cobro.
     * Soporta lazy loading con skip y limit.
     * Retorna tuplas (CobroDineroProgramado, Sale) ordenadas por creadoEl más antiguo a más reciente.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE cdp.estaCobrado = false
           AND s.tipoVenta = 'CREDITO'
           AND s.estaPagadoEntregado = false
           RETURN cdp, s
           ORDER BY cdp.creadoEl ASC
           SKIP $skip LIMIT $limit
           """)
    Flux<Object[]> findCobroCreditoPendientes(@Param("skip") int skip, @Param("limit") int limit);

    /**
     * Encuentra CobroDineroProgramado con Sale de tipo PEDIDO pendientes de cobro.
     * Retorna tuplas (CobroDineroProgramado, Sale) ordenadas por creadoEl más antiguo a más reciente.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE cdp.estaCobrado = false
           AND s.tipoVenta = 'PEDIDO'
           AND s.estaPagadoEntregado = false
           RETURN cdp, s
           ORDER BY cdp.creadoEl ASC
           """)
    Flux<Object[]> findCobroPedidoPendientes();

    /**
     * Cuenta el total de CobroDineroProgramado con Sale de tipo CREDITO pendientes.
     * Útil para implementar lazy loading.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           WHERE cdp.estaCobrado = false
           AND s.tipoVenta = 'CREDITO'
           AND s.estaPagadoEntregado = false
           RETURN count(cdp)
           """)
    Mono<Long> countCobroCreditoPendientes();

    /**
     * Relaciona un Dinero con un CobroDineroProgramado.
     */
    @Query("""
           MATCH (cdp:CobroDineroProgramado) WHERE cdp.id = $cobroDineroProgramadoId
           MATCH (d:Dinero) WHERE d.id = $dineroId
           CREATE (cdp)-[:CON_DINERO_COBRADO]->(d)
           RETURN cdp.id
           """)
    Mono<UUID> addDineroToCobroDineroProgramado(@Param("cobroDineroProgramadoId") UUID cobroDineroProgramadoId, @Param("dineroId") UUID dineroId);
}
