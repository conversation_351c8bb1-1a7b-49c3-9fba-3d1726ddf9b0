package corp.jamaro.jamaroservidor.app.caja.repository;
import corp.jamaro.jamaroservidor.app.caja.model.Caja;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface CajaRepository extends ReactiveNeo4jRepository<Caja, UUID> {

    /**
     * Relaciona un Dinero con las entradas de una Caja.
     */
    @Query("""
           MATCH (c:Caja) WHERE c.id = $cajaId
           MATCH (d:Dinero) WHERE d.id = $dineroId
           CREATE (c)-[:CON_ENTRADA_CAJA]->(d)
           RETURN c.id
           """)
    Mono<UUID> addDineroToEntradas(@Param("cajaId") UUID cajaId, @Param("dineroId") UUID dineroId);

    /**
     * Verifica si una Caja está abierta (estaCerrada = false).
     */
    @Query("""
           MATCH (c:Caja) WHERE c.id = $cajaId
           RETURN c.estaCerrada = false
           """)
    Mono<Boolean> isCajaAbierta(@Param("cajaId") UUID cajaId);
}
