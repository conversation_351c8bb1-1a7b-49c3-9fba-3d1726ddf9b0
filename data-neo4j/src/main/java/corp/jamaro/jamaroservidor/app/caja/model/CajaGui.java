package corp.jamaro.jamaroservidor.app.caja.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Set;
import java.util.UUID;

@Data
@Node
public class CajaGui {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "CON_CAJA")
    private Caja caja;

    @Relationship(type = "CON_COBRO_CONTADO_PENDIENTE")
    private Set<CobroDineroProgramado> cobrosContadoPendientes;//CobroDineroProgramado que tengan relacion con un Sale con tipoVenta = CONTADO
    // y que no se han cobrado por completo estaCobrado = false ordenados por fechaLimite de más antigua a más recientes

    @Relationship(type = "CON_COBRO_CREDITO_PENDIENTE")
    private Set<CobroDineroProgramado> cobrosCreditoPendientes;//CobroDineroProgramado que tengan relacion con un Sale con tipoVenta = CREDITO
    // y que no se han cobrado por completo estaCobrado = false



    private String guiConfig; // un json con las preferencias del usuario para su gui (color de usuario, tamaña letra, etc)
}
