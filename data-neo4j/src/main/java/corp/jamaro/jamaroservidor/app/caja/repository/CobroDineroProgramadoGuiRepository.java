package corp.jamaro.jamaroservidor.app.caja.repository;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface CobroDineroProgramadoGuiRepository extends ReactiveNeo4jRepository<CobroDineroProgramadoGui, UUID> {

    /**
     * Busca CobroDineroProgramadoGui por username del propietario.
     */
    Mono<CobroDineroProgramadoGui> findByUsernameOwner(String username);
}
