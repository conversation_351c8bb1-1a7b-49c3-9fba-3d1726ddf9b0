package corp.jamaro.jamaroservidor.app.caja.repository;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface CobroDineroProgramadoGuiRepository extends ReactiveNeo4jRepository<CobroDineroProgramadoGui, UUID> {

    /**
     * Encuentra CobroDineroProgramadoGui por username del propietario.
     */
    @Query("""
           MATCH (gui:CobroDineroProgramadoGui {usernameOwner: $username})
           RETURN gui
           """)
    Mono<CobroDineroProgramadoGui> findByUsernameOwner(@Param("username") String username);

    /**
     * Verifica si existe CobroDineroProgramadoGui para un username.
     */
    @Query("""
           MATCH (gui:CobroDineroProgramadoGui {usernameOwner: $username})
           RETURN count(gui) > 0
           """)
    Mono<Boolean> existsByUsernameOwner(@Param("username") String username);
}
