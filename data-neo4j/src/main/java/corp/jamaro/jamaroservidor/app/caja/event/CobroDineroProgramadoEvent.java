package corp.jamaro.jamaroservidor.app.caja.event;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;

import java.time.Instant;
import java.util.UUID;

/**
 * Eventos relacionados con CobroDineroProgramado.
 * Utiliza el patrón de eventos de Spring para desacoplar la lógica de negocio.
 */
public sealed interface CobroDineroProgramadoEvent permits 
    CobroDineroProgramadoEvent.Created,
    CobroDineroProgramadoEvent.Updated,
    CobroDineroProgramadoEvent.Completed {

    UUID cobroDineroProgramadoId();
    UUID saleId();
    Sale.TipoVenta tipoVenta();
    Instant timestamp();

    /**
     * Evento emitido cuando se crea un nuevo CobroDineroProgramado.
     */
    record Created(
            UUID cobroDineroProgramadoId,
            UUID saleId,
            Sale.TipoVenta tipoVenta,
            Double montoACobrar,
            String iniciadoPor,
            Instant timestamp
    ) implements CobroDineroProgramadoEvent {
        public Created(CobroDineroProgramado cobro, Sale sale) {
            this(cobro.getId(), sale.getId(), sale.getTipoVenta(), 
                 cobro.getMontoACobrar(), cobro.getIniciadoPor(), Instant.now());
        }
    }

    /**
     * Evento emitido cuando se actualiza un CobroDineroProgramado (cobro parcial).
     */
    record Updated(
            UUID cobroDineroProgramadoId,
            UUID saleId,
            Sale.TipoVenta tipoVenta,
            Double montoRestante,
            Double montoCobrado,
            String tramitadoPor,
            Instant timestamp
    ) implements CobroDineroProgramadoEvent {
        public Updated(CobroDineroProgramado cobro, Sale sale, Double montoCobrado, String tramitadoPor) {
            this(cobro.getId(), sale.getId(), sale.getTipoVenta(),
                 cobro.getMontoRestante(), montoCobrado, tramitadoPor, Instant.now());
        }
    }

    /**
     * Evento emitido cuando se completa un CobroDineroProgramado (estaCobrado = true).
     */
    record Completed(
            UUID cobroDineroProgramadoId,
            UUID saleId,
            Sale.TipoVenta tipoVenta,
            Double montoTotal,
            String tramitadoPor,
            Instant timestamp
    ) implements CobroDineroProgramadoEvent {
        public Completed(CobroDineroProgramado cobro, Sale sale, String tramitadoPor) {
            this(cobro.getId(), sale.getId(), sale.getTipoVenta(),
                 cobro.getMontoACobrar(), tramitadoPor, Instant.now());
        }
    }
}
