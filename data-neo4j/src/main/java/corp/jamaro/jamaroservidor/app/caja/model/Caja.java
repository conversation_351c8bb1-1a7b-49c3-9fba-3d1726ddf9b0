package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Caja {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String nombreCaja; // Por si luego ponemos varias cajas

    //inicio
    @Relationship(type = "ABRE_CAJA")
    private User abiertaPor; // Username del que abre la caja
    private Instant abiertaEl = Instant.now();

    private Double montoInicial; // en soles

    private Integer InicioDiezCentimos;//monedas de 10 centimos
    private Integer InicioVeinteCentimos;//monedas de 20 centimos
    private Integer InicioCincuentaCentimos;//monedas de 50 centimos
    private Integer InicioUnSol;//monedas de 1 sol
    private Integer InicioDosSoles;//monedas de 2 soles
    private Integer InicioCincoSoles;//monedas de 5 soles
    private Integer InicioDiezSoles;//billetes de 10 soles
    private Integer InicioVeinteSoles;//billetes de 20 soles
    private Integer InicioCincuentaSoles;//billetes de 50 soles
    private Integer InicioCienSoles;//billetes de 100 soles
    private Integer InicioDoscientosSoles;//billetes de 200 soles

    //Cierre
    @Relationship(type = "CIERRA_CAJA")
    private User cerradaPor;// User que cierra la caja

    private Instant cerradaEl;
    private Instant entregadaEl;//fecha que entrega el dinero para llevar al banco
    private Boolean estaEntregada = false;// campo para controlar que la caja fue cerrada y entregada

    private Double totalEntradasEfectivo;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = true y tipoDeDinero = EFECTIVO
    private Double totalEntradasDigital;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = true y tipoDeDinero = DIGITAL

    private Double totalSalidasEfectivo;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = false y tipoDeDinero = EFECTIVO
    private Double totalSalidasDigital;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = false y tipoDeDinero = DIGITAL

    private Double CierreDineroEfectivo; // totalEntradasEfectivo - totalSalidasEfectivo + montoInicial (dinero del tipo Efectivo)
    private Double CierreDineroDigital;// totalEntradasDigital - totalSalidasDigital (dinero del tipo Digital)

    private Double montoEfectivoDeclaradoPorUsuario; // Para comparar si cuadra la caja es El dinero efectivo presente en la caja.
    private Double diferenciaEfectivo; // CierreDineroEfectivo - montoEfectivoDeclaradoPorUsuario

    private Integer CierreDiezCentimos;//monedas de 10 centimos
    private Integer CierreVeinteCentimos;//monedas de 20 centimos
    private Integer CierreCincuentaCentimos;//monedas de 50 centimos
    private Integer CierreUnSol;//monedas de 1 sol
    private Integer CierreDosSoles;//monedas de 2 soles
    private Integer CierreCincoSoles;//monedas de 5 soles
    private Integer CierreDiezSoles;//billetes de 10 soles
    private Integer CierreVeinteSoles;//billetes de 20 soles
    private Integer CierreCincuentaSoles;//billetes de 50 soles
    private Integer CierreCienSoles;//billetes de 100 soles
    private Integer CierreDoscientosSoles;//billetes de 200 soles

    //Datos extra de interes que no se usan pal cálculo de cierre, pero si para estadisticas.
    private Double pagosVentaContadoEfectivo;
    private Double pagosVentaContadoDigital;

    private Double pagosVentaCreditoEfectivo;
    private Double pagosVentaCreditoDigital;

    private Double pagosVentaPedidoEfectivo;
    private Double pagosVentaPedidoDigital;

    private Double devolucionesVentaEfectivo;
    private Double devolucionesVentaDigital;

    // Registros de dinero relacionados a esta caja
    @Relationship(type = "CON_ENTRADA_CAJA")
    private Set<Dinero> entradas; // Entradas asociadas a la caja

    @Relationship(type = "CON_SALIDA_CAJA")
    private Set<Dinero> salidas; // Salidas asociadas a la caja

    @Relationship(type = "CON_GASTO_EXTRA_CAJA")
    private Set<GastoExtraDinero> gastosExtra; // Gastos extra asociados a la caja

    @Relationship(type = "CON_INGRESO_EXTRA_CAJA")
    private Set<IngresoExtraDinero> ingresosExtra; // Ingresos extra asociados a la caja

    private Boolean estaCerrada = false;
}
