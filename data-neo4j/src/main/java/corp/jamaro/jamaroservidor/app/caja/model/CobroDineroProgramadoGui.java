package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Modelo GUI para gestionar CobroDineroProgramado en tiempo real.
 * Permite a los clientes suscribirse a actualizaciones de cobros pendientes
 * filtrados por tipo de venta (CONTADO, CREDITO, PEDIDO).
 */
@Data
@Node
public class CobroDineroProgramadoGui {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String usernameOwner; // Usuario propietario de esta GUI

    // CobroDineroProgramado con Sale.tipoVenta = CONTADO, estaCobrado = false, estaPagadoEntregado = false
    // Ordenados por creadoEl (más antiguo a más reciente)
    @Relationship(type = "CON_COBRO_CONTADO_PENDIENTE")
    private List<CobroDineroProgramadoWithSale> cobrosContadoPendientes;

    // CobroDineroProgramado con Sale.tipoVenta = CREDITO, estaCobrado = false, estaPagadoEntregado = false
    // Ordenados por creadoEl (más antiguo a más reciente)
    // Soporta lazy loading de 30 en 30
    @Relationship(type = "CON_COBRO_CREDITO_PENDIENTE")
    private List<CobroDineroProgramadoWithSale> cobrosCreditoPendientes;

    // CobroDineroProgramado con Sale.tipoVenta = PEDIDO, estaCobrado = false, estaPagadoEntregado = false
    // Ordenados por creadoEl (más antiguo a más reciente)
    @Relationship(type = "CON_COBRO_PEDIDO_PENDIENTE")
    private List<CobroDineroProgramadoWithSale> cobrosPedidoPendientes;

    private Instant createdAt = Instant.now();
    private Instant lastUpdated = Instant.now();

    /**
     * Clase interna para representar la combinación CobroDineroProgramado + Sale
     * que se necesita en la GUI para mostrar detalles completos.
     */
    @Data
    @Node
    public static class CobroDineroProgramadoWithSale {
        @Id
        @GeneratedValue(GeneratedValue.UUIDGenerator.class)
        private UUID id;

        @Relationship(type = "CON_COBRO_DINERO_PROGRAMADO")
        private CobroDineroProgramado cobroDineroProgramado;

        @Relationship(type = "CON_SALE")
        private Sale sale;

        private Instant createdAt = Instant.now();
    }
}
