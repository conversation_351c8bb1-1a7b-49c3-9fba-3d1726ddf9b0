package corp.jamaro.jamaroservidor.app.caja.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;

/**
 * GUI Model para manejar la visualización en tiempo real de CobroDineroProgramado.
 * Permite a los clientes suscribirse a actualizaciones de cobros pendientes
 * filtrados por tipo de venta (CONTADO, CREDITO, PEDIDO).
 *
 * Este modelo actúa como un marcador para la GUI del usuario.
 * Los datos reales se obtienen dinámicamente a través de consultas en el servicio.
 */
@Data
@Node
public class CobroDineroProgramadoGui {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String usernameOwner; // Usuario propietario de esta GUI

    private Instant createdAt = Instant.now();
    private Instant lastUpdated = Instant.now();
}
