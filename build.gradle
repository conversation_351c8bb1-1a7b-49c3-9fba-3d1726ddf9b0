plugins {
    id 'java' // Keep java plugin if there's still some code in src/main/java of the main app
    id 'org.springframework.boot' version '3.4.0'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'org.graalvm.buildtools.native' version '0.9.28' // Keep if GraalVM native compilation is used
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Dependencies on the new modules
    implementation project(':data-neo4j')
    implementation project(':rsocket')
    implementation project(':security')
    implementation project(':minio')

    // Lombok (if used in main app's classes, e.g. JamaroServidorApplication.java)
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    // For @ConfigurationProperties in main app
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    // Development (DevTools) - usually kept in the main application module
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // Test dependencies for the main application (if any tests remain here)
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test' // If reactor types are tested here
    testImplementation 'org.springframework.security:spring-security-test' // If security aspects are tested here
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

}

tasks.named('test') {
    useJUnitPlatform()
}

// GraalVM native image configuration (if used)
// tasks.named('bootBuildImage') { ... }
