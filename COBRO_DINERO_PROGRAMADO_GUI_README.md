# Sistema de GUI en Tiempo Real para CobroDineroProgramado

## Descripción

Este sistema proporciona una GUI en tiempo real para manejar cobros pendientes de ventas, permitiendo a los clientes suscribirse a actualizaciones automáticas de CobroDineroProgramado filtrados por tipo de venta (CONTADO, CREDITO, PEDIDO).

## Arquitectura

### Tecnologías Utilizadas
- **Spring Boot 3.4.0** con Java 21
- **Spring Data Neo4j** para persistencia moderna
- **Spring RSocket** para comunicación en tiempo real
- **Spring Events** para emisiones automáticas
- **Neo4j 5.26.1** como base de datos

### Componentes Principales

1. **CobroDineroProgramadoGui** - Modelo GUI simplificado
2. **CobroDineroProgramadoEvent** - Eventos para actualizaciones automáticas
3. **CobroDineroProgramadoGuiService** - Lógica de negocio y suscripciones
4. **CobroDineroProgramadoService** - Servicio para realizar cobros
5. **CobroDineroProgramadoGuiController** - Endpoints RSocket
6. **CobroDineroProgramadoEventListener** - Manejo automático de eventos

## Endpoints RSocket

### Suscripciones

#### `cobroDineroProgramadoGui.subscribe`
Suscripción automática para el usuario autenticado.
```java
// Cliente se suscribe automáticamente
Flux<CobroDineroProgramadoGui> subscribeToChanges()
```

#### `cobroDineroProgramadoGui.subscribeById`
Suscripción específica por ID de GUI.
```java
Flux<CobroDineroProgramadoGui> subscribeToChanges(UUID guiId)
```

### Consultas de Datos

#### `cobroDineroProgramadoGui.getCobrosContadoPendientes`
Obtiene cobros CONTADO pendientes (lista completa).
```java
Flux<CobroDineroProgramadoWithSaleProjection> getCobrosContadoPendientes()
```

#### `cobroDineroProgramadoGui.getCobrosCreditoPendientes`
Obtiene cobros CREDITO pendientes con lazy loading (30 en 30).
```java
Flux<CobroDineroProgramadoWithSaleProjection> getCobrosCreditoPendientes(PaginationRequest request)

// Request:
record PaginationRequest(int page, int size) {}
```

#### `cobroDineroProgramadoGui.countCobrosCreditoPendientes`
Cuenta total de cobros CREDITO pendientes.
```java
Mono<Long> countCobrosCreditoPendientes()
```

#### `cobroDineroProgramadoGui.getCobrosPedidoPendientes`
Obtiene cobros PEDIDO pendientes (lista completa).
```java
Flux<CobroDineroProgramadoWithSaleProjection> getCobrosPedidoPendientes()
```

### Operaciones de Cobro

#### `cobroDineroProgramadoGui.realizarCobro`
Realiza un cobro para un CobroDineroProgramado específico.
```java
Mono<Boolean> realizarCobro(CobroRequest request)

// Request:
record CobroRequest(
    UUID cobroDineroProgramadoId,
    UUID cajaId,
    Double montoCobrado,
    Dinero.TipoDeDinero tipoDeDinero,
    TipoMoneda tipoMoneda,
    Double factorDeCambio,
    String detalles
) {}
```

## Flujo de Eventos

### Eventos Automáticos

1. **CobroDineroProgramadoEvent.Created** - Se emite cuando se crea un nuevo CobroDineroProgramado
2. **CobroDineroProgramadoEvent.Updated** - Se emite cuando se realiza un cobro parcial
3. **CobroDineroProgramadoEvent.Completed** - Se emite cuando se completa un cobro
4. **CobroDineroProgramadoEvent.SaleCompleted** - Se emite cuando se completa una venta

### Emisiones Automáticas

Los eventos se publican automáticamente cuando:
- Se inicia una venta (CONTADO, CREDITO, PEDIDO) → `Created`
- Se realiza un cobro parcial → `Updated`
- Se completa un cobro → `Completed`
- Se marca una venta como entregada → `SaleCompleted`

## Uso del Sistema

### 1. Suscripción del Cliente

```javascript
// Cliente se suscribe a actualizaciones
const subscription = rsocketClient
    .requestStream({
        route: 'cobroDineroProgramadoGui.subscribe'
    })
    .subscribe({
        onNext: (gui) => {
            console.log('GUI actualizada:', gui);
            // Actualizar interfaz de usuario
        },
        onError: (error) => {
            console.error('Error en suscripción:', error);
        }
    });
```

### 2. Obtener Cobros Pendientes

```javascript
// Cobros CONTADO
rsocketClient
    .requestStream({
        route: 'cobroDineroProgramadoGui.getCobrosContadoPendientes'
    })
    .subscribe({
        onNext: (cobro) => {
            console.log('Cobro CONTADO:', cobro);
        }
    });

// Cobros CREDITO con paginación
rsocketClient
    .requestStream({
        route: 'cobroDineroProgramadoGui.getCobrosCreditoPendientes',
        data: { page: 0, size: 30 }
    })
    .subscribe({
        onNext: (cobro) => {
            console.log('Cobro CREDITO:', cobro);
        }
    });
```

### 3. Realizar Cobro

```javascript
rsocketClient
    .requestResponse({
        route: 'cobroDineroProgramadoGui.realizarCobro',
        data: {
            cobroDineroProgramadoId: 'uuid-del-cobro',
            cajaId: 'uuid-de-la-caja',
            montoCobrado: 100.50,
            tipoDeDinero: 'EFECTIVO',
            tipoMoneda: 'SOLES',
            factorDeCambio: 1.0,
            detalles: 'Cobro en efectivo'
        }
    })
    .subscribe({
        onNext: (success) => {
            if (success) {
                console.log('Cobro realizado exitosamente');
            } else {
                console.log('Error al realizar cobro');
            }
        }
    });
```

## Características Técnicas

### Lazy Loading
- Los cobros CREDITO implementan lazy loading de 30 en 30 elementos
- Los cobros CONTADO y PEDIDO se cargan completos (generalmente menos elementos)

### Tiempo Real
- Todas las actualizaciones se emiten automáticamente usando Spring Events
- Los clientes reciben actualizaciones inmediatas sin necesidad de polling

### Transaccionalidad
- Todas las operaciones de cobro usan TransactionalOperator
- Garantiza consistencia de datos en operaciones complejas

### Seguridad
- Usa SecurityUtils para obtener el usuario autenticado
- El campo `tramitadoPor` se rellena automáticamente

### Rendimiento
- Consultas optimizadas con @Query en Neo4j
- Proyecciones para evitar cargar datos innecesarios
- Sinks con `directBestEffort()` para no bloquear suscriptores lentos

## Integración con Ventas

El sistema se integra automáticamente con el SaleService:
- `iniciarVentaContado()` → Emite evento `Created`
- `iniciarVentaCredito()` → Emite evento `Created`
- `iniciarVentaPedido()` → Emite evento `Created`

Los eventos se procesan automáticamente por el `CobroDineroProgramadoEventListener` que actualiza todas las GUIs suscritas.
