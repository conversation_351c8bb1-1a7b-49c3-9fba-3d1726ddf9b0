package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;

/**
 * Record que representa un par de CobroDineroProgramado y Sale.
 * Se usa para transmitir los datos de cobros pendientes con su venta asociada.
 */
public record CobroPendientePair(
    CobroDineroProgramado cobro,
    Sale sale
) {
    public CobroPendientePair {
        if (cobro == null) {
            throw new IllegalArgumentException("CobroDineroProgramado no puede ser null");
        }
        if (sale == null) {
            throw new IllegalArgumentException("Sale no puede ser null");
        }
    }
}
