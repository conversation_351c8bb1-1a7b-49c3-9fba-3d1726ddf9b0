package corp.jamaro.jamaroservidor.app.caja.event;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;

import java.util.UUID;

/**
 * Eventos relacionados con CobroDineroProgramado para manejo automático
 * de actualizaciones en tiempo real usando Spring Events.
 */
public sealed interface CobroDineroProgramadoEvent {

    /**
     * Evento emitido cuando se crea un nuevo CobroDineroProgramado.
     * Se dispara automáticamente cuando se inicia una venta (CONTADO, CREDITO, PEDIDO).
     */
    record Created(
            UUID cobroDineroProgramadoId,
            UUID saleId,
            Sale.TipoVenta tipoVenta,
            String iniciadoPor
    ) implements CobroDineroProgramadoEvent {}

    /**
     * Evento emitido cuando se actualiza un CobroDineroProgramado.
     * Se dispara cuando se realiza un cobro parcial.
     */
    record Updated(
            UUID cobroDineroProgramadoId,
            UUID saleId,
            Sale.TipoVenta tipoVenta,
            Double montoRestante,
            String tramitadoPor
    ) implements CobroDineroProgramadoEvent {}

    /**
     * Evento emitido cuando se completa un CobroDineroProgramado.
     * Se dispara cuando estaCobrado = true.
     */
    record Completed(
            CobroDineroProgramado cobroDineroProgramado,
            Sale sale,
            String tramitadoPor
    ) implements CobroDineroProgramadoEvent {}

    /**
     * Evento emitido cuando se completa una venta (estaPagadoEntregado = true).
     * Se dispara para actualizar las GUIs y remover de listas pendientes.
     */
    record SaleCompleted(
            UUID saleId,
            Sale.TipoVenta tipoVenta,
            String completadoPor
    ) implements CobroDineroProgramadoEvent {}
}
