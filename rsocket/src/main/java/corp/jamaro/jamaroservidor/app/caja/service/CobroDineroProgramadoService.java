package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.model.Caja;
import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.dinero.repository.DineroRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.UUID;

/**
 * Servicio para realizar cobros de CobroDineroProgramado.
 * Maneja la lógica de negocio para procesar pagos y actualizar estados.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoService {

    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;
    private final DineroRepository dineroRepository;
    private final CajaRepository cajaRepository;
    private final SaleRepository saleRepository;
    private final CobroDineroProgramadoGuiService guiService;
    private final TransactionalOperator transactionalOperator;

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     * 
     * Lógica de negocio:
     * 1. Valida que el CobroDineroProgramado exista y no esté completamente cobrado
     * 2. Valida que la Caja exista y esté abierta
     * 3. Crea Dinero con esEntrada=true y tramitadoPor del usuario actual
     * 4. Relaciona el Dinero con CobroDineroProgramado y Caja.entradas
     * 5. Actualiza montoRestante del CobroDineroProgramado
     * 6. Si montoRestante <= 0, marca estaCobrado=true y terminadoDeCobrarEl
     * 7. Actualiza Sale.totalRestante
     * 8. Si Sale.totalRestante <= 0, marca estaPagadoEntregado=true
     * 9. Emite actualizaciones a las GUIs
     * 
     * @param cobroDineroProgramadoId ID del CobroDineroProgramado a cobrar
     * @param cajaId ID de la Caja donde se realiza el cobro
     * @param montoCobrado Monto a cobrar (en soles)
     * @param tipoDeDinero Tipo de dinero (EFECTIVO o DIGITAL)
     * @param detalles Detalles del pago (opcional)
     * @return Mono<Boolean> true si el cobro fue exitoso, false en caso de error
     */
    public Mono<Boolean> realizarCobro(UUID cobroDineroProgramadoId, UUID cajaId, Double montoCobrado, 
                                      Dinero.TipoDeDinero tipoDeDinero, String detalles) {
        log.debug("Realizando cobro para CobroDineroProgramado ID: {}, monto: {}", cobroDineroProgramadoId, montoCobrado);

        // Validaciones fuera de la transacción
        return validateCobroInput(cobroDineroProgramadoId, cajaId, montoCobrado)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(user -> user.getUsername()))
                .flatMap(username -> {
                    // OPERACIÓN TRANSACCIONAL: Todas las modificaciones dentro de la transacción
                    return processCobroTransaction(cobroDineroProgramadoId, cajaId, montoCobrado, 
                                                 tipoDeDinero, detalles, username)
                            .as(transactionalOperator::transactional);
                })
                .flatMap(success -> {
                    if (success) {
                        // Emitir actualizaciones a las GUIs después de la transacción exitosa
                        return guiService.emitToAllGuis().thenReturn(true);
                    }
                    return Mono.just(false);
                })
                .onErrorResume(e -> {
                    log.error("Error al realizar cobro: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Valida los datos de entrada para el cobro.
     */
    private Mono<Boolean> validateCobroInput(UUID cobroDineroProgramadoId, UUID cajaId, Double montoCobrado) {
        if (montoCobrado == null || montoCobrado <= 0) {
            return Mono.error(new IllegalArgumentException("El monto a cobrar debe ser mayor a 0"));
        }

        return cobroDineroProgramadoRepository.existsById(cobroDineroProgramadoId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("CobroDineroProgramado no encontrado"));
                    }
                    return cajaRepository.existsById(cajaId);
                })
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Caja no encontrada"));
                    }
                    return Mono.just(true);
                });
    }

    /**
     * Procesa la transacción completa del cobro.
     */
    private Mono<Boolean> processCobroTransaction(UUID cobroDineroProgramadoId, UUID cajaId, Double montoCobrado,
                                                Dinero.TipoDeDinero tipoDeDinero, String detalles, String username) {
        
        return cobroDineroProgramadoRepository.findById(cobroDineroProgramadoId)
                .flatMap(cobro -> {
                    if (cobro.getEstaCobrado()) {
                        return Mono.error(new IllegalArgumentException("El cobro ya está completado"));
                    }

                    // Crear Dinero de entrada
                    Dinero dinero = createDineroEntrada(montoCobrado, tipoDeDinero, detalles, username);
                    
                    return dineroRepository.save(dinero)
                            .flatMap(savedDinero -> {
                                // Relacionar Dinero con CobroDineroProgramado
                                return addDineroToCobroDineroProgramado(cobro, savedDinero)
                                        .flatMap(updatedCobro -> {
                                            // Relacionar Dinero con Caja.entradas
                                            return addDineroToCajaEntradas(cajaId, savedDinero.getId())
                                                    .flatMap(cajaUpdated -> {
                                                        // Actualizar estados del cobro y sale
                                                        return updateCobroAndSaleStates(updatedCobro);
                                                    });
                                        });
                            });
                });
    }

    /**
     * Crea un objeto Dinero para entrada de cobro.
     */
    private Dinero createDineroEntrada(Double montoCobrado, Dinero.TipoDeDinero tipoDeDinero, String detalles, String username) {
        Dinero dinero = new Dinero();
        dinero.setTramitadoPor(username);
        dinero.setTipoDeMoneda(Dinero.TipoMoneda.SOLES);
        dinero.setMontoAntesDelCambio(montoCobrado);
        dinero.setFactorDeCambio(1.0);
        dinero.setMontoReal(montoCobrado);
        dinero.setTipoDeDinero(tipoDeDinero);
        dinero.setDetalles(detalles);
        dinero.setEsEntrada(true);
        return dinero;
    }

    /**
     * Agrega Dinero al CobroDineroProgramado y actualiza montoRestante.
     */
    private Mono<CobroDineroProgramado> addDineroToCobroDineroProgramado(CobroDineroProgramado cobro, Dinero dinero) {
        // Crear relación entre CobroDineroProgramado y Dinero
        return cobroDineroProgramadoRepository.addDineroToCobroDineroProgramado(cobro.getId(), dinero.getId())
                .then(Mono.defer(() -> {
                    // Actualizar montoRestante
                    Double nuevoMontoRestante = cobro.getMontoRestante() - dinero.getMontoReal();
                    cobro.setMontoRestante(Math.max(0.0, nuevoMontoRestante));

                    // Si está completamente cobrado, actualizar estado
                    if (cobro.getMontoRestante() <= 0) {
                        cobro.setEstaCobrado(true);
                        cobro.setTerminadoDeCobrarEl(Instant.now());
                    }

                    return cobroDineroProgramadoRepository.save(cobro);
                }));
    }

    /**
     * Relaciona Dinero con Caja.entradas usando query.
     */
    private Mono<Boolean> addDineroToCajaEntradas(UUID cajaId, UUID dineroId) {
        log.debug("Relacionando Dinero ID: {} con Caja.entradas ID: {}", dineroId, cajaId);
        return cajaRepository.addDineroToEntradas(cajaId, dineroId)
                .map(id -> true)
                .onErrorReturn(false);
    }

    /**
     * Actualiza estados del Sale relacionado al CobroDineroProgramado.
     */
    private Mono<Boolean> updateCobroAndSaleStates(CobroDineroProgramado cobro) {
        // Encontrar Sale relacionado y actualizar totalRestante
        return saleRepository.findSaleByCobroDineroProgramado(cobro.getId())
                .flatMap(sale -> {
                    // Recalcular totalRestante del Sale
                    return saleRepository.updateSaleTotalRestante(sale.getId())
                            .map(updatedSale -> true);
                })
                .defaultIfEmpty(true);
    }
}
