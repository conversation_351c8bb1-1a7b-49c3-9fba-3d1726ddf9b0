package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.event.CobroDineroProgramadoEvent;
import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.dinero.model.enums.TipoMoneda;
import corp.jamaro.jamaroservidor.app.dinero.repository.DineroRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.UUID;

/**
 * Servicio para realizar cobros de CobroDineroProgramado.
 * Maneja la lógica de negocio para cobrar dinero y actualizar estados.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoService {

    private final CobroDineroProgramadoRepository cobroRepository;
    private final DineroRepository dineroRepository;
    private final CajaRepository cajaRepository;
    private final SaleRepository saleRepository;
    private final TransactionalOperator transactionalOperator;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     * 
     * @param cobroDineroProgramadoId ID del CobroDineroProgramado
     * @param cajaId ID de la Caja donde se registra el cobro
     * @param montoCobrado Monto a cobrar
     * @param tipoDeDinero Tipo de dinero (EFECTIVO, DIGITAL)
     * @param tipoMoneda Tipo de moneda (SOLES, DOLARES, etc.)
     * @param factorDeCambio Factor de cambio si no es moneda base
     * @param detalles Detalles adicionales del cobro
     * @return Mono<Boolean> true si el cobro fue exitoso
     */
    public Mono<Boolean> realizarCobro(
            UUID cobroDineroProgramadoId,
            UUID cajaId,
            Double montoCobrado,
            Dinero.TipoDeDinero tipoDeDinero,
            TipoMoneda tipoMoneda,
            Double factorDeCambio,
            String detalles) {

        log.info("Iniciando cobro para CobroDineroProgramado ID: {}, monto: {}", 
                cobroDineroProgramadoId, montoCobrado);

        // Validaciones fuera de la transacción
        return validateCobroInput(cobroDineroProgramadoId, cajaId, montoCobrado)
                .flatMap(valid -> SecurityUtils.getCurrentUser().map(user -> user.getUsername()))
                .flatMap(username -> {
                    // OPERACIÓN TRANSACCIONAL: Todas las modificaciones dentro de la transacción
                    return processCobroTransaction(
                            cobroDineroProgramadoId, cajaId, montoCobrado, 
                            tipoDeDinero, tipoMoneda, factorDeCambio, detalles, username)
                            .as(transactionalOperator::transactional);
                })
                .map(success -> success)
                .onErrorResume(e -> {
                    log.error("Error al realizar cobro: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Métodos internos
    // ─────────────────────────────────────────────────────────────────────────────

    /**
     * Valida los datos de entrada para el cobro.
     */
    private Mono<Boolean> validateCobroInput(UUID cobroDineroProgramadoId, UUID cajaId, Double montoCobrado) {
        if (cobroDineroProgramadoId == null) {
            return Mono.error(new IllegalArgumentException("ID de CobroDineroProgramado es requerido"));
        }
        if (cajaId == null) {
            return Mono.error(new IllegalArgumentException("ID de Caja es requerido"));
        }
        if (montoCobrado == null || montoCobrado <= 0) {
            return Mono.error(new IllegalArgumentException("Monto a cobrar debe ser mayor a 0"));
        }

        // Verificar que el CobroDineroProgramado existe y no está completamente cobrado
        return cobroRepository.findByIdWithRelations(cobroDineroProgramadoId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("CobroDineroProgramado no encontrado")))
                .flatMap(cobro -> {
                    if (cobro.getEstaCobrado()) {
                        return Mono.error(new IllegalStateException("El cobro ya está completado"));
                    }
                    if (montoCobrado > cobro.getMontoRestante()) {
                        return Mono.error(new IllegalArgumentException(
                                "Monto a cobrar (" + montoCobrado + ") excede el monto restante (" + cobro.getMontoRestante() + ")"));
                    }
                    return Mono.just(true);
                })
                .flatMap(valid -> {
                    // Verificar que la Caja existe y está abierta
                    return cajaRepository.findById(cajaId)
                            .switchIfEmpty(Mono.error(new IllegalArgumentException("Caja no encontrada")))
                            .flatMap(caja -> {
                                if (caja.getEstaCerrada()) {
                                    return Mono.error(new IllegalStateException("La caja está cerrada"));
                                }
                                return Mono.just(true);
                            });
                });
    }

    /**
     * Procesa la transacción completa del cobro.
     */
    private Mono<Boolean> processCobroTransaction(
            UUID cobroDineroProgramadoId, UUID cajaId, Double montoCobrado,
            Dinero.TipoDeDinero tipoDeDinero, TipoMoneda tipoMoneda, Double factorDeCambio,
            String detalles, String username) {

        return cobroRepository.findByIdWithRelations(cobroDineroProgramadoId)
                .flatMap(cobro -> {
                    // Paso 1: Crear el Dinero (entrada)
                    return createDineroEntrada(montoCobrado, tipoDeDinero, tipoMoneda, factorDeCambio, detalles, username)
                            .flatMap(dinero -> {
                                // Paso 2: Relacionar Dinero con Caja (entradas)
                                return createCajaDineroRelationship(cajaId, dinero.getId())
                                        .flatMap(success -> {
                                            // Paso 3: Relacionar Dinero con CobroDineroProgramado
                                            return createCobroDineroRelationship(cobroDineroProgramadoId, dinero.getId())
                                                    .flatMap(relationSuccess -> {
                                                        // Paso 4: Actualizar montoRestante del CobroDineroProgramado
                                                        return updateCobroMontoRestante(cobro, montoCobrado, username);
                                                    });
                                        });
                            });
                });
    }

    /**
     * Crea un registro de Dinero como entrada.
     */
    private Mono<Dinero> createDineroEntrada(Double montoCobrado, Dinero.TipoDeDinero tipoDeDinero, 
                                           TipoMoneda tipoMoneda, Double factorDeCambio, String detalles, String username) {
        var dinero = new Dinero();
        dinero.setTramitadoPor(username);
        dinero.setTipoDeMoneda(tipoMoneda);
        dinero.setMontoAntesDelCambio(montoCobrado);
        dinero.setFactorDeCambio(factorDeCambio != null ? factorDeCambio : 1.0);
        dinero.setMontoReal(montoCobrado * dinero.getFactorDeCambio());
        dinero.setTipoDeDinero(tipoDeDinero);
        dinero.setDetalles(detalles);
        dinero.setEsEntrada(true); // Es una entrada de dinero
        dinero.setCreatedAt(Instant.now());

        return dineroRepository.save(dinero);
    }

    /**
     * Crea relación entre Caja y Dinero (entradas).
     */
    private Mono<Boolean> createCajaDineroRelationship(UUID cajaId, UUID dineroId) {
        return cajaRepository.createCajaEntradaDineroRelationship(cajaId, dineroId)
                .map(id -> true)
                .doOnSuccess(success -> log.debug("Relación Caja-Dinero creada: {} -> {}", cajaId, dineroId));
    }

    /**
     * Crea relación entre CobroDineroProgramado y Dinero.
     */
    private Mono<Boolean> createCobroDineroRelationship(UUID cobroId, UUID dineroId) {
        return cobroRepository.createCobroDineroRelationship(cobroId, dineroId)
                .map(id -> true)
                .doOnSuccess(success -> log.debug("Relación CobroDineroProgramado-Dinero creada: {} -> {}", cobroId, dineroId));
    }

    /**
     * Actualiza el montoRestante del CobroDineroProgramado y publica eventos.
     */
    private Mono<Boolean> updateCobroMontoRestante(CobroDineroProgramado cobro, Double montoCobrado, String username) {
        Double nuevoMontoRestante = cobro.getMontoRestante() - montoCobrado;
        boolean estaCompletado = nuevoMontoRestante <= 0.0;

        cobro.setMontoRestante(Math.max(0.0, nuevoMontoRestante));
        
        if (estaCompletado) {
            cobro.setEstaCobrado(true);
            cobro.setTerminadoDeCobrarEl(Instant.now());
        }

        return cobroRepository.save(cobro)
                .flatMap(savedCobro -> {
                    // Encontrar Sale relacionado y actualizar totalRestante
                    return saleRepository.findSaleByCobroDineroProgramado(savedCobro.getId())
                            .flatMap(sale -> {
                                // Recalcular totalRestante del Sale
                                return saleRepository.updateSaleTotalRestante(sale.getId())
                                        .doOnSuccess(updatedSale -> {
                                            // Publicar evento según el estado del cobro
                                            if (estaCompletado) {
                                                eventPublisher.publishEvent(new CobroDineroProgramadoEvent.Completed(savedCobro, updatedSale, username));
                                            } else {
                                                eventPublisher.publishEvent(new CobroDineroProgramadoEvent.Updated(
                                                        savedCobro.getId(), updatedSale.getId(), updatedSale.getTipoVenta(), 
                                                        savedCobro.getMontoRestante(), username));
                                            }
                                        });
                            });
                })
                .thenReturn(true);
    }
}
