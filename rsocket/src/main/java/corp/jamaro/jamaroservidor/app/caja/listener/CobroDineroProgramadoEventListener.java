package corp.jamaro.jamaroservidor.app.caja.listener;

import corp.jamaro.jamaroservidor.app.caja.event.CobroDineroProgramadoEvent;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoGuiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Event Listener para manejar eventos de CobroDineroProgramado
 * y actualizar automáticamente las GUIs suscritas en tiempo real.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoEventListener {

    private final CobroDineroProgramadoGuiService guiService;

    /**
     * Maneja eventos de creación de CobroDineroProgramado.
     * Emite actualizaciones a todas las GUIs suscritas.
     */
    @EventListener
    @Async
    public void handleCobroDineroProgramadoCreated(CobroDineroProgramadoEvent.Created event) {
        log.debug("Procesando evento de creación de CobroDineroProgramado: {}", event.cobroDineroProgramadoId());
        
        guiService.emitToAllGuis()
                .doOnSuccess(v -> log.debug("GUI actualizada por evento de creación: {}", event.cobroDineroProgramadoId()))
                .doOnError(error -> log.error("Error actualizando GUI por evento de creación: {}", error.getMessage()))
                .subscribe();
    }

    /**
     * Maneja eventos de actualización de CobroDineroProgramado.
     * Emite actualizaciones a todas las GUIs suscritas.
     */
    @EventListener
    @Async
    public void handleCobroDineroProgramadoUpdated(CobroDineroProgramadoEvent.Updated event) {
        log.debug("Procesando evento de actualización de CobroDineroProgramado: {}", event.cobroDineroProgramadoId());
        
        guiService.emitToAllGuis()
                .doOnSuccess(v -> log.debug("GUI actualizada por evento de actualización: {}", event.cobroDineroProgramadoId()))
                .doOnError(error -> log.error("Error actualizando GUI por evento de actualización: {}", error.getMessage()))
                .subscribe();
    }

    /**
     * Maneja eventos de completado de CobroDineroProgramado.
     * Emite actualizaciones a todas las GUIs suscritas.
     */
    @EventListener
    @Async
    public void handleCobroDineroProgramadoCompleted(CobroDineroProgramadoEvent.Completed event) {
        log.debug("Procesando evento de completado de CobroDineroProgramado: {}", event.cobroDineroProgramado().getId());
        
        guiService.emitToAllGuis()
                .doOnSuccess(v -> log.debug("GUI actualizada por evento de completado: {}", event.cobroDineroProgramado().getId()))
                .doOnError(error -> log.error("Error actualizando GUI por evento de completado: {}", error.getMessage()))
                .subscribe();
    }

    /**
     * Maneja eventos de venta completada.
     * Emite actualizaciones a todas las GUIs suscritas para remover de listas pendientes.
     */
    @EventListener
    @Async
    public void handleSaleCompleted(CobroDineroProgramadoEvent.SaleCompleted event) {
        log.debug("Procesando evento de venta completada: {}", event.saleId());
        
        guiService.emitToAllGuis()
                .doOnSuccess(v -> log.debug("GUI actualizada por evento de venta completada: {}", event.saleId()))
                .doOnError(error -> log.error("Error actualizando GUI por evento de venta completada: {}", error.getMessage()))
                .subscribe();
    }
}
