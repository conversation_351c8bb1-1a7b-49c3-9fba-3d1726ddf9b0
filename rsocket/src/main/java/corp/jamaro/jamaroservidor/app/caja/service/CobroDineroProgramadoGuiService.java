package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoGuiRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Servicio para gestionar CobroDineroProgramadoGui en tiempo real.
 * Permite suscripciones a actualizaciones de cobros pendientes por tipo de venta.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiService {

    private final CobroDineroProgramadoGuiRepository guiRepository;
    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;

    // Mapa de Sinks para emisiones en tiempo real por GUI ID
    private final Map<UUID, Sinks.Many<CobroDineroProgramadoGui>> guiSinks = new ConcurrentHashMap<>();

    /**
     * Permite suscribirse a actualizaciones de CobroDineroProgramadoGui por username.
     * Si no existe GUI para el usuario, la crea automáticamente.
     */
    public Flux<CobroDineroProgramadoGui> subscribeToChanges(String username) {
        log.info("Suscribiendo a cambios de CobroDineroProgramadoGui para usuario: {}", username);

        return getOrCreateGuiForUser(username)
                .flatMapMany(gui -> {
                    var sink = getOrCreateSink(gui.getId());
                    
                    // Cargar datos iniciales y emitir
                    return loadGuiData(gui)
                            .flatMapMany(loadedGui -> {
                                // Emitir estado inicial
                                sink.tryEmitNext(loadedGui);
                                
                                // Retornar flujo que incluye estado inicial + actualizaciones futuras
                                return sink.asFlux().startWith(loadedGui);
                            });
                });
    }

    /**
     * Obtiene CobroDineroProgramadoGui para un usuario o la crea si no existe.
     */
    private Mono<CobroDineroProgramadoGui> getOrCreateGuiForUser(String username) {
        return guiRepository.findByUsernameOwner(username)
                .switchIfEmpty(createNewGuiForUser(username));
    }

    /**
     * Crea nueva CobroDineroProgramadoGui para un usuario.
     */
    private Mono<CobroDineroProgramadoGui> createNewGuiForUser(String username) {
        log.debug("Creando nueva CobroDineroProgramadoGui para usuario: {}", username);
        
        CobroDineroProgramadoGui newGui = new CobroDineroProgramadoGui();
        newGui.setUsernameOwner(username);
        newGui.setCobrosContadoPendientes(new ArrayList<>());
        newGui.setCobrosCreditoPendientes(new ArrayList<>());
        newGui.setCobrosPedidoPendientes(new ArrayList<>());
        
        return guiRepository.save(newGui);
    }

    /**
     * Carga todos los datos de cobros pendientes para la GUI.
     */
    private Mono<CobroDineroProgramadoGui> loadGuiData(CobroDineroProgramadoGui gui) {
        log.debug("Cargando datos para CobroDineroProgramadoGui ID: {}", gui.getId());

        return Mono.zip(
                loadCobroContadoPendientes(),
                loadCobroCreditoPendientes(0, 30), // Primeros 30 para crédito
                loadCobroPedidoPendientes()
        ).map(tuple -> {
            gui.setCobrosContadoPendientes(tuple.getT1());
            gui.setCobrosCreditoPendientes(tuple.getT2());
            gui.setCobrosPedidoPendientes(tuple.getT3());
            gui.setLastUpdated(Instant.now());
            return gui;
        });
    }

    /**
     * Carga cobros de contado pendientes.
     */
    private Mono<List<CobroDineroProgramadoGui.CobroDineroProgramadoWithSale>> loadCobroContadoPendientes() {
        return cobroDineroProgramadoRepository.findCobroContadoPendientes()
                .map(this::mapToCobroDineroProgramadoWithSale)
                .collectList();
    }

    /**
     * Carga cobros de crédito pendientes con lazy loading.
     */
    private Mono<List<CobroDineroProgramadoGui.CobroDineroProgramadoWithSale>> loadCobroCreditoPendientes(int skip, int limit) {
        return cobroDineroProgramadoRepository.findCobroCreditoPendientes(skip, limit)
                .map(this::mapToCobroDineroProgramadoWithSale)
                .collectList();
    }

    /**
     * Carga cobros de pedido pendientes.
     */
    private Mono<List<CobroDineroProgramadoGui.CobroDineroProgramadoWithSale>> loadCobroPedidoPendientes() {
        return cobroDineroProgramadoRepository.findCobroPedidoPendientes()
                .map(this::mapToCobroDineroProgramadoWithSale)
                .collectList();
    }

    /**
     * Mapea Object[] (resultado de query) a CobroDineroProgramadoWithSale.
     */
    private CobroDineroProgramadoGui.CobroDineroProgramadoWithSale mapToCobroDineroProgramadoWithSale(Object[] result) {
        CobroDineroProgramado cobro = (CobroDineroProgramado) result[0];
        Sale sale = (Sale) result[1];
        
        CobroDineroProgramadoGui.CobroDineroProgramadoWithSale cobroWithSale = 
            new CobroDineroProgramadoGui.CobroDineroProgramadoWithSale();
        cobroWithSale.setCobroDineroProgramado(cobro);
        cobroWithSale.setSale(sale);
        
        return cobroWithSale;
    }

    /**
     * Obtiene o crea Sink para una GUI específica.
     */
    private Sinks.Many<CobroDineroProgramadoGui> getOrCreateSink(UUID guiId) {
        return guiSinks.computeIfAbsent(guiId, 
            id -> Sinks.many().multicast().directBestEffort());
    }

    /**
     * Emite actualizaciones a todas las GUIs suscritas.
     * Se llama cuando se crean, modifican o completan CobroDineroProgramado.
     */
    public Mono<Void> emitToAllGuis() {
        log.debug("Emitiendo actualizaciones a todas las GUIs suscritas");
        
        return guiRepository.findAll()
                .flatMap(this::loadGuiData)
                .doOnNext(gui -> {
                    var sink = guiSinks.get(gui.getId());
                    if (sink != null) {
                        sink.tryEmitNext(gui);
                        log.debug("Actualización emitida para GUI ID: {}", gui.getId());
                    }
                })
                .then();
    }

    /**
     * Carga más cobros de crédito (lazy loading).
     */
    public Mono<List<CobroDineroProgramadoGui.CobroDineroProgramadoWithSale>> loadMoreCreditoCobros(String username, int skip, int limit) {
        log.debug("Cargando más cobros de crédito para usuario: {}, skip: {}, limit: {}", username, skip, limit);
        return loadCobroCreditoPendientes(skip, limit);
    }

    /**
     * Obtiene el total de cobros de crédito pendientes.
     */
    public Mono<Long> getTotalCreditoCobros() {
        return cobroDineroProgramadoRepository.countCobroCreditoPendientes();
    }
}
