package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.dto.CobroDineroProgramadoGuiDto;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Servicio para manejar la GUI de CobroDineroProgramado en tiempo real.
 * Proporciona suscripciones reactivas para diferentes tipos de venta.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiService {

    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;

    // Sink para emisiones en tiempo real - un solo sink para toda la GUI
    private final Sinks.Many<CobroDineroProgramadoGuiDto> guiUpdateSink = 
        Sinks.many().multicast().directBestEffort();

    // Cache para evitar múltiples suscripciones al mismo sink
    private final ConcurrentHashMap<String, Boolean> activeSubscriptions = new ConcurrentHashMap<>();

    /**
     * Se suscribe a los cambios de la GUI de cobros pendientes.
     * Retorna el estado actual y luego las actualizaciones en tiempo real.
     */
    public Flux<CobroDineroProgramadoGuiDto> subscribeToChanges() {
        log.info("Nueva suscripción a cambios de CobroDineroProgramadoGui");
        
        // Obtener estado actual
        Mono<CobroDineroProgramadoGuiDto> currentState = getCurrentGuiState();
        
        // Flujo de actualizaciones futuras
        Flux<CobroDineroProgramadoGuiDto> updates = guiUpdateSink.asFlux();
        
        // Concatenar estado actual con actualizaciones
        return currentState.concatWith(updates);
    }

    /**
     * Obtiene los cobros de crédito con paginación (lazy loading).
     */
    public Mono<CobroDineroProgramadoGuiDto> getCreditosPage(int page, int size) {
        log.info("Obteniendo página {} de créditos pendientes (tamaño: {})", page, size);
        
        int skip = page * size;
        
        return Mono.zip(
            // Obtener cobros paginados
            cobroDineroProgramadoRepository.findCobrosCreditoPendientes(skip, size)
                .map(cobro -> {
                    // Aquí necesitamos extraer el Sale de la relación
                    // Por ahora usamos un placeholder, luego lo optimizaremos
                    return CobroDineroProgramadoGuiDto.CobroPendienteDto.from(cobro, null);
                })
                .collectList(),
            
            // Contar total
            cobroDineroProgramadoRepository.countCobrosCreditoPendientes()
        )
        .map(tuple -> {
            var cobros = tuple.getT1();
            var total = tuple.getT2();
            
            var dto = new CobroDineroProgramadoGuiDto();
            dto.setCobrosCreditoPendientes(cobros);
            
            boolean hasMore = (skip + size) < total;
            dto.setCreditoPagination(new CobroDineroProgramadoGuiDto.PaginationInfo(
                page, size, hasMore, total
            ));
            
            return dto;
        });
    }

    /**
     * Emite una actualización de la GUI completa.
     * Se llama cuando hay cambios en los cobros pendientes.
     */
    public Mono<Void> emitGuiUpdate() {
        log.debug("Emitiendo actualización de CobroDineroProgramadoGui");
        
        return getCurrentGuiState()
            .doOnNext(dto -> {
                guiUpdateSink.tryEmitNext(dto);
                log.info("Actualización de GUI emitida exitosamente");
            })
            .then();
    }

    /**
     * Obtiene el estado actual completo de la GUI.
     */
    private Mono<CobroDineroProgramadoGuiDto> getCurrentGuiState() {
        return Mono.zip(
            // Cobros de contado
            cobroDineroProgramadoRepository.findCobrosContadoPendientes()
                .map(cobro -> {
                    // TODO: Extraer Sale de la relación cargada por la query
                    return CobroDineroProgramadoGuiDto.CobroPendienteDto.from(cobro, null);
                })
                .collectList(),
            
            // Cobros de crédito (primera página)
            cobroDineroProgramadoRepository.findCobrosCreditoPendientes(0, 30)
                .map(cobro -> {
                    // TODO: Extraer Sale de la relación cargada por la query
                    return CobroDineroProgramadoGuiDto.CobroPendienteDto.from(cobro, null);
                })
                .collectList(),
            
            // Cobros de pedido
            cobroDineroProgramadoRepository.findCobrosPedidoPendientes()
                .map(cobro -> {
                    // TODO: Extraer Sale de la relación cargada por la query
                    return CobroDineroProgramadoGuiDto.CobroPendienteDto.from(cobro, null);
                })
                .collectList(),
            
            // Total de créditos para paginación
            cobroDineroProgramadoRepository.countCobrosCreditoPendientes()
        )
        .map(tuple -> {
            var contados = tuple.getT1();
            var creditos = tuple.getT2();
            var pedidos = tuple.getT3();
            var totalCreditos = tuple.getT4();
            
            var dto = new CobroDineroProgramadoGuiDto();
            dto.setCobrosContadoPendientes(contados);
            dto.setCobrosCreditoPendientes(creditos);
            dto.setCobrosPedidoPendientes(pedidos);
            
            // Información de paginación para créditos
            boolean hasMoreCreditos = creditos.size() >= 30 && totalCreditos > 30;
            dto.setCreditoPagination(new CobroDineroProgramadoGuiDto.PaginationInfo(
                0, 30, hasMoreCreditos, totalCreditos
            ));
            
            return dto;
        });
    }
}
