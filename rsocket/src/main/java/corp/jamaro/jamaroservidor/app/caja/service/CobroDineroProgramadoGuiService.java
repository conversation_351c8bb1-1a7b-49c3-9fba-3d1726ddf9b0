package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoGuiRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Servicio para manejar la GUI en tiempo real de CobroDineroProgramado.
 * Utiliza Spring Data Neo4j moderno y Spring Events para actualizaciones automáticas.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiService {

    private final CobroDineroProgramadoGuiRepository guiRepository;
    private final CobroDineroProgramadoRepository cobroRepository;

    // Mapa de Sinks para cada GUI activa (por ID de GUI)
    private final Map<UUID, Sinks.Many<CobroDineroProgramadoGui>> guiSinks = new ConcurrentHashMap<>();

    /**
     * Permite a los clientes suscribirse a actualizaciones de CobroDineroProgramado
     * según el usuario autenticado. Crea la GUI si no existe.
     */
    public Flux<CobroDineroProgramadoGui> subscribeToChanges() {
        return SecurityUtils.getCurrentUser()
                .flatMap(user -> getOrCreateGuiForUser(user.getUsername()))
                .flatMapMany(gui -> {
                    var sink = getOrCreateSink(gui.getId());
                    return sink.asFlux().startWith(gui);
                });
    }

    /**
     * Suscripción específica por ID de GUI (para casos especiales).
     */
    public Flux<CobroDineroProgramadoGui> subscribeToChanges(UUID guiId) {
        return guiRepository.findById(guiId)
                .flatMapMany(gui -> {
                    var sink = getOrCreateSink(gui.getId());
                    return sink.asFlux().startWith(gui);
                })
                .switchIfEmpty(Flux.error(new IllegalArgumentException("GUI no encontrada con ID: " + guiId)));
    }

    /**
     * Obtiene cobros CONTADO pendientes en tiempo real.
     */
    public Flux<CobroDineroProgramadoRepository.CobroDineroProgramadoWithSaleProjection> getCobrosContadoPendientes() {
        return cobroRepository.findCobrosContadoPendientes();
    }

    /**
     * Obtiene cobros CREDITO pendientes con lazy loading.
     */
    public Flux<CobroDineroProgramadoRepository.CobroDineroProgramadoWithSaleProjection> getCobrosCreditoPendientes(int page, int size) {
        int skip = page * size;
        return cobroRepository.findCobrosCreditoPendientes(skip, size);
    }

    /**
     * Cuenta total de cobros CREDITO pendientes para paginación.
     */
    public Mono<Long> countCobrosCreditoPendientes() {
        return cobroRepository.countCobrosCreditoPendientes();
    }

    /**
     * Obtiene cobros PEDIDO pendientes en tiempo real.
     */
    public Flux<CobroDineroProgramadoRepository.CobroDineroProgramadoWithSaleProjection> getCobrosPedidoPendientes() {
        return cobroRepository.findCobrosPedidoPendientes();
    }

    /**
     * Emite actualizaciones a todas las GUIs suscritas.
     * Llamado automáticamente por los Event Listeners.
     */
    public Mono<Void> emitToAllGuis() {
        log.debug("Emitiendo actualizaciones a todas las GUIs suscritas");
        
        return guiRepository.findAll()
                .flatMap(this::loadGuiWithData)
                .doOnNext(gui -> {
                    var sink = guiSinks.get(gui.getId());
                    if (sink != null) {
                        sink.tryEmitNext(gui);
                        log.debug("Actualización emitida para GUI ID: {}", gui.getId());
                    }
                })
                .then();
    }

    /**
     * Emite actualización específica para una GUI.
     */
    public Mono<Void> emitToGui(UUID guiId) {
        return guiRepository.findById(guiId)
                .flatMap(this::loadGuiWithData)
                .doOnNext(gui -> {
                    var sink = guiSinks.get(gui.getId());
                    if (sink != null) {
                        sink.tryEmitNext(gui);
                        log.debug("Actualización emitida para GUI específica ID: {}", gui.getId());
                    }
                })
                .then();
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Métodos internos
    // ─────────────────────────────────────────────────────────────────────────────

    /**
     * Obtiene o crea una GUI para el usuario especificado.
     */
    private Mono<CobroDineroProgramadoGui> getOrCreateGuiForUser(String username) {
        return guiRepository.findByUsernameOwner(username)
                .switchIfEmpty(createNewGuiForUser(username));
    }

    /**
     * Crea una nueva GUI para el usuario.
     */
    private Mono<CobroDineroProgramadoGui> createNewGuiForUser(String username) {
        log.info("Creando nueva CobroDineroProgramadoGui para usuario: {}", username);
        
        var newGui = new CobroDineroProgramadoGui();
        newGui.setUsernameOwner(username);
        newGui.setCreatedAt(Instant.now());
        newGui.setLastUpdated(Instant.now());
        
        return guiRepository.save(newGui);
    }

    /**
     * Carga la GUI con todos sus datos relacionados.
     */
    private Mono<CobroDineroProgramadoGui> loadGuiWithData(CobroDineroProgramadoGui gui) {
        gui.setLastUpdated(Instant.now());
        return Mono.just(gui);
    }

    /**
     * Obtiene o crea un Sink para la GUI especificada.
     */
    private Sinks.Many<CobroDineroProgramadoGui> getOrCreateSink(UUID guiId) {
        return guiSinks.computeIfAbsent(guiId, id -> {
            log.debug("Creando nuevo Sink para GUI ID: {}", id);
            return Sinks.many().multicast().directBestEffort();
        });
    }
}
