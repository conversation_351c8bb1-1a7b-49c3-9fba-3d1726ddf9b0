package corp.jamaro.jamaroservidor.app.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Configuración para habilitar el procesamiento asíncrono de eventos.
 * Permite que los listeners de eventos se ejecuten de forma asíncrona.
 */
@Configuration
@EnableAsync
public class AsyncConfig {
    // Spring Boot autoconfigura un TaskExecutor por defecto
    // Si necesitas personalizar el executor, puedes definir un @Bean aquí
}
