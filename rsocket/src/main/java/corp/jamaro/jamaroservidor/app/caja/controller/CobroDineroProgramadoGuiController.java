package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoGuiService;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoService;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

/**
 * Controlador RSocket para gestionar CobroDineroProgramadoGui en tiempo real.
 *
 * Endpoints disponibles:
 * - cobroDineroProgramadoGui.subscribe: Suscribirse a actualizaciones de cobros pendientes
 * - cobroDineroProgramadoGui.loadMoreCredito: Cargar más cobros de crédito (lazy loading)
 * - cobroDineroProgramadoGui.getTotalCredito: Obtener total de cobros de crédito pendientes
 * - cobroDineroProgramadoGui.realizarCobro: Realizar cobro de un CobroDineroProgramado
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiController {

    private final CobroDineroProgramadoGuiService guiService;
    private final CobroDineroProgramadoService cobroService;

    /**
     * Permite suscribirse a actualizaciones de CobroDineroProgramadoGui.
     * Automáticamente obtiene el usuario del contexto de seguridad.
     * 
     * @return Flux que emite CobroDineroProgramadoGui con actualizaciones en tiempo real
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribe")
    public Flux<CobroDineroProgramadoGui> subscribeToChanges() {
        log.info("Solicitud de suscripción a CobroDineroProgramadoGui recibida");
        
        return SecurityUtils.getCurrentUser()
                .map(user -> user.getUsername())
                .flatMapMany(username -> {
                    log.info("Suscribiendo usuario: {} a CobroDineroProgramadoGui", username);
                    return guiService.subscribeToChanges(username);
                })
                .doOnError(error -> log.error("Error en suscripción a CobroDineroProgramadoGui: {}", error.getMessage()));
    }

    /**
     * Carga más cobros de crédito para lazy loading.
     * 
     * @param request Objeto con skip y limit para paginación
     * @return Mono con lista de cobros de crédito adicionales
     */
    @MessageMapping("cobroDineroProgramadoGui.loadMoreCredito")
    public Mono<List<CobroDineroProgramadoGui.CobroDineroProgramadoWithSale>> loadMoreCredito(@Payload LazyLoadRequest request) {
        log.info("Cargando más cobros de crédito - skip: {}, limit: {}", request.skip(), request.limit());
        
        return SecurityUtils.getCurrentUser()
                .map(user -> user.getUsername())
                .flatMap(username -> guiService.loadMoreCreditoCobros(username, request.skip(), request.limit()))
                .doOnError(error -> log.error("Error cargando más cobros de crédito: {}", error.getMessage()));
    }

    /**
     * Obtiene el total de cobros de crédito pendientes.
     * Útil para implementar paginación en el cliente.
     * 
     * @return Mono con el número total de cobros de crédito pendientes
     */
    @MessageMapping("cobroDineroProgramadoGui.getTotalCredito")
    public Mono<Long> getTotalCredito() {
        log.info("Obteniendo total de cobros de crédito pendientes");
        
        return guiService.getTotalCreditoCobros()
                .doOnError(error -> log.error("Error obteniendo total de cobros de crédito: {}", error.getMessage()));
    }

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     *
     * @param request Datos del cobro a realizar
     * @return Mono<Boolean> true si el cobro fue exitoso, false en caso de error
     */
    @MessageMapping("cobroDineroProgramadoGui.realizarCobro")
    public Mono<Boolean> realizarCobro(@Payload CobroRequest request) {
        log.info("Realizando cobro para CobroDineroProgramado ID: {}, monto: {}",
                request.cobroDineroProgramadoId(), request.montoCobrado());

        return cobroService.realizarCobro(
                request.cobroDineroProgramadoId(),
                request.cajaId(),
                request.montoCobrado(),
                request.tipoDeDinero(),
                request.detalles()
        ).doOnError(error -> log.error("Error realizando cobro: {}", error.getMessage()));
    }

    /**
     * Record para manejar requests de lazy loading.
     */
    public record LazyLoadRequest(int skip, int limit) {}

    /**
     * Record para manejar requests de cobro.
     */
    public record CobroRequest(
            UUID cobroDineroProgramadoId,
            UUID cajaId,
            Double montoCobrado,
            Dinero.TipoDeDinero tipoDeDinero,
            String detalles
    ) {}
}
