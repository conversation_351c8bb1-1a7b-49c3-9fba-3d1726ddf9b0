package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramadoGui;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoGuiService;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoService;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.dinero.model.enums.TipoMoneda;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Controlador RSocket para la GUI de CobroDineroProgramado.
 * Proporciona endpoints para suscripciones en tiempo real y operaciones de cobro.
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiController {

    private final CobroDineroProgramadoGuiService guiService;
    private final CobroDineroProgramadoService cobroService;

    /**
     * Permite suscribirse a actualizaciones de CobroDineroProgramado en tiempo real.
     * Crea automáticamente la GUI para el usuario autenticado si no existe.
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribe")
    public Flux<CobroDineroProgramadoGui> subscribeToChanges() {
        log.info("Cliente suscribiéndose a actualizaciones de CobroDineroProgramado");
        return guiService.subscribeToChanges();
    }

    /**
     * Suscripción específica por ID de GUI.
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribeById")
    public Flux<CobroDineroProgramadoGui> subscribeToChanges(@Payload UUID guiId) {
        log.info("Cliente suscribiéndose a GUI específica ID: {}", guiId);
        return guiService.subscribeToChanges(guiId);
    }

    /**
     * Obtiene cobros CONTADO pendientes.
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosContadoPendientes")
    public Flux<CobroDineroProgramadoRepository.CobroDineroProgramadoWithSaleProjection> getCobrosContadoPendientes() {
        log.info("Obteniendo cobros CONTADO pendientes");
        return guiService.getCobrosContadoPendientes();
    }

    /**
     * Obtiene cobros CREDITO pendientes con paginación (lazy loading).
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosCreditoPendientes")
    public Flux<CobroDineroProgramadoRepository.CobroDineroProgramadoWithSaleProjection> getCobrosCreditoPendientes(@Payload PaginationRequest request) {
        log.info("Obteniendo cobros CREDITO pendientes - página: {}, tamaño: {}", request.page(), request.size());
        return guiService.getCobrosCreditoPendientes(request.page(), request.size());
    }

    /**
     * Cuenta total de cobros CREDITO pendientes.
     */
    @MessageMapping("cobroDineroProgramadoGui.countCobrosCreditoPendientes")
    public Mono<Long> countCobrosCreditoPendientes() {
        log.info("Contando cobros CREDITO pendientes");
        return guiService.countCobrosCreditoPendientes();
    }

    /**
     * Obtiene cobros PEDIDO pendientes.
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosPedidoPendientes")
    public Flux<CobroDineroProgramadoRepository.CobroDineroProgramadoWithSaleProjection> getCobrosPedidoPendientes() {
        log.info("Obteniendo cobros PEDIDO pendientes");
        return guiService.getCobrosPedidoPendientes();
    }

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     */
    @MessageMapping("cobroDineroProgramadoGui.realizarCobro")
    public Mono<Boolean> realizarCobro(@Payload CobroRequest request) {
        log.info("Realizando cobro para CobroDineroProgramado ID: {}, monto: {}", 
                request.cobroDineroProgramadoId(), request.montoCobrado());
        
        return cobroService.realizarCobro(
                request.cobroDineroProgramadoId(),
                request.cajaId(),
                request.montoCobrado(),
                request.tipoDeDinero(),
                request.tipoMoneda(),
                request.factorDeCambio(),
                request.detalles()
        );
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Records para requests
    // ─────────────────────────────────────────────────────────────────────────────

    /**
     * Request para paginación.
     */
    public record PaginationRequest(
            int page,
            int size
    ) {}

    /**
     * Request para realizar un cobro.
     */
    public record CobroRequest(
            UUID cobroDineroProgramadoId,
            UUID cajaId,
            Double montoCobrado,
            Dinero.TipoDeDinero tipoDeDinero,
            TipoMoneda tipoMoneda,
            Double factorDeCambio,
            String detalles
    ) {}
}
