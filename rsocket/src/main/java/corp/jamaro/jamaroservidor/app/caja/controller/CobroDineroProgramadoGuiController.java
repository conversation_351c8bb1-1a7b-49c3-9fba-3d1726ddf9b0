package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.dto.CobroDineroProgramadoGuiDto;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoGuiService;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoService;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.security.enums.RoleEnum;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Controlador RSocket para la GUI de CobroDineroProgramado.
 * Accesible solo para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiController {

    private final CobroDineroProgramadoGuiService cobroDineroProgramadoGuiService;
    private final CobroDineroProgramadoService cobroDineroProgramadoService;

    /**
     * Permite suscribirse a los cambios de la GUI de cobros pendientes.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     * 
     * @return Flux que emite el estado actual y las actualizaciones de la GUI
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribe")
    public Flux<CobroDineroProgramadoGuiDto> subscribeToChanges() {
        log.info("Solicitud de suscripción a CobroDineroProgramadoGui recibida");
        
        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para acceder a CobroDineroProgramadoGui");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }
                
                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} suscribiéndose a CobroDineroProgramadoGui", user.getUsername()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.subscribeToChanges());
            });
    }

    /**
     * Obtiene una página específica de cobros de crédito (lazy loading).
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     * 
     * @param request Objeto con información de paginación
     * @return Mono que emite la página solicitada
     */
    @MessageMapping("cobroDineroProgramadoGui.getCreditosPage")
    public Mono<CobroDineroProgramadoGuiDto> getCreditosPage(@Payload CreditoPageRequest request) {
        log.info("Solicitud de página {} de créditos (tamaño: {})", request.page(), request.size());
        
        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para obtener página de créditos");
                    return Mono.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }
                
                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} solicitando página {} de créditos", 
                        user.getUsername(), request.page()))
                    .flatMap(user -> cobroDineroProgramadoGuiService.getCreditosPage(request.page(), request.size()));
            });
    }

    /**
     * Fuerza una actualización manual de la GUI.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     * 
     * @return Mono que completa cuando la actualización se emite
     */
    @MessageMapping("cobroDineroProgramadoGui.refresh")
    public Mono<Void> refreshGui() {
        log.info("Solicitud de actualización manual de CobroDineroProgramadoGui");
        
        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para refrescar GUI");
                    return Mono.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }
                
                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} solicitando refresh de GUI", user.getUsername()))
                    .flatMap(user -> cobroDineroProgramadoGuiService.emitGuiUpdate());
            });
    }

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Datos del cobro a realizar
     * @return Mono que emite true si el cobro fue exitoso
     */
    @MessageMapping("cobroDineroProgramadoGui.realizarCobro")
    public Mono<Boolean> realizarCobro(@Payload CobroRequest request) {
        log.info("Solicitud de cobro para CobroDineroProgramado {} por monto {}",
            request.cobroId(), request.montoCobrado());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para realizar cobros");
                    return Mono.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} realizando cobro para CobroDineroProgramado {}",
                        user.getUsername(), request.cobroId()))
                    .flatMap(user -> cobroDineroProgramadoService.realizarCobro(
                        request.cobroId(),
                        request.cajaId(),
                        request.montoCobrado(),
                        request.tipoDeDinero(),
                        request.detalles()
                    ));
            });
    }

    /**
     * Record para la solicitud de paginación de créditos.
     */
    public record CreditoPageRequest(int page, int size) {
        public CreditoPageRequest {
            if (page < 0) {
                throw new IllegalArgumentException("La página no puede ser negativa");
            }
            if (size <= 0 || size > 100) {
                throw new IllegalArgumentException("El tamaño debe estar entre 1 y 100");
            }
        }
    }

    /**
     * Record para la solicitud de cobro.
     */
    public record CobroRequest(
        java.util.UUID cobroId,
        java.util.UUID cajaId,
        Double montoCobrado,
        Dinero.TipoDeDinero tipoDeDinero,
        String detalles
    ) {
        public CobroRequest {
            if (cobroId == null) {
                throw new IllegalArgumentException("El ID del cobro es requerido");
            }
            if (cajaId == null) {
                throw new IllegalArgumentException("El ID de la caja es requerido");
            }
            if (montoCobrado == null || montoCobrado <= 0) {
                throw new IllegalArgumentException("El monto debe ser mayor a 0");
            }
            if (tipoDeDinero == null) {
                throw new IllegalArgumentException("El tipo de dinero es requerido");
            }
        }
    }
}
