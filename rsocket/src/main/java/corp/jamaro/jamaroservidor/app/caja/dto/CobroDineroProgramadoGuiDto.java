package corp.jamaro.jamaroservidor.app.caja.dto;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * DTO para la GUI de CobroDineroProgramado en tiempo real.
 * Contiene listas separadas por tipo de venta para optimizar la visualización.
 */
@Data
public class CobroDineroProgramadoGuiDto {
    
    /**
     * Lista de cobros pendientes para ventas de CONTADO.
     * Ordenados de más antiguo a más reciente.
     */
    private List<CobroPendienteDto> cobrosContadoPendientes;
    
    /**
     * Lista de cobros pendientes para ventas de CREDITO.
     * Ordenados de más antiguo a más reciente.
     * Soporta lazy loading de 30 en 30.
     */
    private List<CobroPendienteDto> cobrosCreditoPendientes;
    
    /**
     * Lista de cobros pendientes para ventas de PEDIDO.
     * Ordenados de más antiguo a más reciente.
     */
    private List<CobroPendienteDto> cobrosPedidoPendientes;
    
    /**
     * Información de paginación para créditos (lazy loading).
     */
    private PaginationInfo creditoPagination;
    
    /**
     * DTO interno que representa un cobro pendiente con información del Sale asociado.
     */
    @Data
    public static class CobroPendienteDto {
        // Datos del CobroDineroProgramado
        private UUID cobroId;
        private Double montoACobrar;
        private Double montoRestante;
        private String iniciadoPor;
        private Instant creadoEl;
        private Instant fechaLimite;
        private Boolean estaCobrado;
        
        // Datos del Sale asociado
        private UUID saleId;
        private String saleIniciadaPor;
        private Double saleTotalMontoAcordado;
        private Double saleTotalRestante;
        private Sale.TipoVenta saleTipoVenta;
        private Boolean saleEstaPagadoEntregado;
        private Instant saleCreatedAt;
        
        // Información del cliente (si existe)
        private String clienteNombre;
        private String clienteDocumento;
        
        public static CobroPendienteDto from(CobroDineroProgramado cobro, Sale sale) {
            CobroPendienteDto dto = new CobroPendienteDto();
            
            // Datos del cobro
            dto.setCobroId(cobro.getId());
            dto.setMontoACobrar(cobro.getMontoACobrar());
            dto.setMontoRestante(cobro.getMontoRestante());
            dto.setIniciadoPor(cobro.getIniciadoPor());
            dto.setCreadoEl(cobro.getCreadoEl());
            dto.setFechaLimite(cobro.getFechaLimite());
            dto.setEstaCobrado(cobro.getEstaCobrado());
            
            // Datos del sale
            dto.setSaleId(sale.getId());
            dto.setSaleIniciadaPor(sale.getIniciadaPor());
            dto.setSaleTotalMontoAcordado(sale.getTotalMontoAcordado());
            dto.setSaleTotalRestante(sale.getTotalRestante());
            dto.setSaleTipoVenta(sale.getTipoVenta());
            dto.setSaleEstaPagadoEntregado(sale.getEstaPagadoEntregado());
            dto.setSaleCreatedAt(sale.getCreatedAt());
            
            // Datos del cliente (si existe)
            if (sale.getCliente() != null) {
                dto.setClienteNombre(sale.getCliente().getNombre());
                dto.setClienteDocumento(sale.getCliente().getDocumento());
            }
            
            return dto;
        }
    }
    
    /**
     * Información de paginación para lazy loading.
     */
    @Data
    public static class PaginationInfo {
        private int currentPage;
        private int pageSize;
        private boolean hasMore;
        private long totalElements;
        
        public PaginationInfo(int currentPage, int pageSize, boolean hasMore, long totalElements) {
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.hasMore = hasMore;
            this.totalElements = totalElements;
        }
    }
}
